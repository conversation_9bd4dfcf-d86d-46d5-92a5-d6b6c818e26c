#!/usr/bin/env python3
"""
Stage-3 Segmentation: YOLO + SAM for Mouth Mask Generation

Processes motion-refined crops to generate per-frame mouth masks using:
1. YOLO face detection for face priors
2. Motion analysis for mouth region identification  
3. SAM (Segment Anything Model) for precise mask generation

Usage:
    python -u scripts/segment_yolo_sam.py \
      --input_dir "data/stage1M_motion_refined_full" \
      --output_dir "data/stage3_segmented" \
      --gallery "reports/stage3_gallery.html" \
      --summary "reports/stage3_summary.csv" \
      --manifest "data/manifests/stage3_manifest.csv" \
      --yolo_model "yolov8n-face.pt" \
      --sam_model "vit_b" \
      --size 96 --upsample 2.0 --motion_percentile 85 \
      --min_mask_coverage 0.015 --max_mask_coverage 0.12 \
      --min_frames 12 --stride 2 --workers 4

Requirements:
    opencv-python, numpy, tqdm, torch, torchvision, ultralytics, segment-anything, jinja2
"""

import argparse
import cv2
import numpy as np
import os
import csv
import json
import multiprocessing as mp
from pathlib import Path
from tqdm import tqdm
from typing import List, Tuple, Optional, Dict, Any
import torch
from jinja2 import Template
import base64
import warnings
warnings.filterwarnings('ignore')

# Import dependencies
try:
    from ultralytics import YOLO
    import segment_anything as sam
    from segment_anything import SamPredictor, sam_model_registry
except ImportError as e:
    print(f"❌ Missing dependencies: {e}")
    print("Install with: pip install ultralytics segment-anything")
    exit(1)


def find_videos(input_dir: str, extensions: List[str]) -> List[Path]:
    """Find all video files recursively."""
    input_path = Path(input_dir)
    videos = []
    for ext in extensions:
        videos.extend(input_path.rglob(f'*{ext}'))
        videos.extend(input_path.rglob(f'*{ext.upper()}'))
    return sorted(videos)


def compute_optical_flow_magnitude(prev_gray: np.ndarray, curr_gray: np.ndarray) -> np.ndarray:
    """Compute optical flow magnitude using Farneback method."""
    try:
        flow = cv2.calcOpticalFlowPyrLK(
            prev_gray, curr_gray,
            pyr_scale=0.5, levels=3, winsize=15,
            iterations=3, poly_n=5, poly_sigma=1.2
        )
        magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)
        return magnitude.astype(np.float32)
    except:
        # Fallback: frame difference
        diff = cv2.absdiff(prev_gray, curr_gray)
        return diff.astype(np.float32)


def get_motion_hotspot(motion_heatmap: np.ndarray, percentile: int, 
                      face_box: Optional[Tuple[int, int, int, int]] = None) -> Tuple[int, int, int, int]:
    """Extract motion hotspot bounding box, optionally constrained by face box."""
    h, w = motion_heatmap.shape
    
    # If face box provided, zero motion outside it
    if face_box is not None:
        x0, y0, x1, y1 = face_box
        mask = np.zeros_like(motion_heatmap)
        mask[y0:y1, x0:x1] = 1
        motion_heatmap = motion_heatmap * mask
    
    # Zero out top 30% to focus on lower face/mouth region
    top_30_percent = int(0.3 * h)
    motion_heatmap[:top_30_percent, :] = 0
    
    # Threshold at percentile
    if np.max(motion_heatmap) > 0:
        threshold = np.percentile(motion_heatmap[motion_heatmap > 0], percentile)
        binary_mask = (motion_heatmap >= threshold).astype(np.uint8)
    else:
        binary_mask = np.zeros_like(motion_heatmap, dtype=np.uint8)
    
    # Morphological operations
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)
    
    # Find largest connected component
    contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w_box, h_box = cv2.boundingRect(largest_contour)
        # Expand by 1.2x
        center_x, center_y = x + w_box//2, y + h_box//2
        new_w, new_h = int(w_box * 1.2), int(h_box * 1.2)
        x0 = max(0, center_x - new_w//2)
        y0 = max(0, center_y - new_h//2)
        x1 = min(w, center_x + new_w//2)
        y1 = min(h, center_y + new_h//2)
        return x0, y0, x1, y1
    else:
        # Return center region as fallback
        return w//4, h//4, 3*w//4, 3*h//4


def detect_face_yolo(yolo_model, frame: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
    """Detect face using YOLO and return dominant bounding box."""
    try:
        results = yolo_model(frame, verbose=False)
        if len(results) > 0 and len(results[0].boxes) > 0:
            # Get the largest face box
            boxes = results[0].boxes.xyxy.cpu().numpy()
            areas = [(box[2] - box[0]) * (box[3] - box[1]) for box in boxes]
            largest_idx = np.argmax(areas)
            box = boxes[largest_idx]
            return int(box[0]), int(box[1]), int(box[2]), int(box[3])
    except Exception as e:
        print(f"YOLO detection error: {e}")
    return None


def sample_points_in_box(box: Tuple[int, int, int, int], num_points: int = 5) -> List[Tuple[int, int]]:
    """Sample random points inside a bounding box for SAM prompts."""
    x0, y0, x1, y1 = box
    points = []
    for _ in range(num_points):
        x = np.random.randint(x0, x1)
        y = np.random.randint(y0, y1)
        points.append((x, y))
    return points


def interpolate_masks(masks: List[np.ndarray], stride: int) -> List[np.ndarray]:
    """Interpolate masks for skipped frames using nearest neighbor."""
    if stride <= 1:
        return masks
    
    interpolated = []
    for i in range(len(masks) * stride):
        source_idx = min(i // stride, len(masks) - 1)
        interpolated.append(masks[source_idx].copy())
    
    return interpolated


def smooth_masks(masks: List[np.ndarray]) -> List[np.ndarray]:
    """Apply morphological smoothing to mask sequence."""
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    smoothed = []
    for mask in masks:
        # Close then open
        smooth = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        smooth = cv2.morphologyEx(smooth, cv2.MORPH_OPEN, kernel)
        smoothed.append(smooth)
    return smoothed


def extract_phrase_from_path(video_path: Path) -> str:
    """Extract phrase from video path structure."""
    parts = video_path.parts
    # Look for phrase in path components
    phrases = ['doctor', 'glasses', 'help', 'i_need_to_move', 'my_mouth_is_dry', 'phone', 'pillow']
    for part in parts:
        if part in phrases:
            return part
        # Handle underscored phrases
        for phrase in phrases:
            if phrase.replace('_', ' ') in part.replace('_', ' '):
                return phrase
    return "unknown"


def process_single_video(args_tuple) -> Dict[str, Any]:
    """Process a single video file - main processing function."""
    (video_path, input_dir, output_dir, yolo_model_path, sam_model_type, 
     size, upsample, motion_percentile, min_mask_coverage, max_mask_coverage, 
     min_frames, stride) = args_tuple
    
    try:
        # Initialize models (will be cached per worker)
        if not hasattr(process_single_video, 'yolo_model'):
            try:
                process_single_video.yolo_model = YOLO(yolo_model_path)
            except:
                print(f"⚠️  YOLO model {yolo_model_path} not found, proceeding without face detection")
                process_single_video.yolo_model = None
        
        if not hasattr(process_single_video, 'sam_predictor'):
            try:
                # Try to load from checkpoints directory first
                checkpoint_path = f"checkpoints/sam_{sam_model_type}.pth"
                if not os.path.exists(checkpoint_path):
                    # Fallback to default SAM download location
                    checkpoint_path = None
                
                sam_model = sam_model_registry[sam_model_type](checkpoint=checkpoint_path)
                if torch.cuda.is_available():
                    sam_model = sam_model.cuda()
                process_single_video.sam_predictor = SamPredictor(sam_model)
            except Exception as e:
                print(f"❌ Failed to load SAM model: {e}")
                return {"error": f"SAM model loading failed: {e}"}
        
        # Setup paths
        rel_path = video_path.relative_to(input_dir)
        output_video_dir = Path(output_dir) / rel_path.parent
        output_video_dir.mkdir(parents=True, exist_ok=True)
        
        video_stem = video_path.stem
        masked_path = output_video_dir / f"{video_stem}_masked.mp4"
        masks_path = output_video_dir / f"{video_stem}_masks.npz"
        overlays_dir = output_video_dir / "overlays"
        overlays_dir.mkdir(exist_ok=True)
        metrics_path = output_video_dir / f"{video_stem}_metrics.json"
        fallback_path = output_video_dir / f"{video_stem}_fallback_unmasked.mp4"
        
        # Read video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return {"error": f"Cannot open video: {video_path}"}
        
        fps = cap.get(cv2.CAP_PROP_FPS) or 25.0
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames < min_frames:
            cap.release()
            return {
                "video_path": str(rel_path),
                "frames": total_frames,
                "fps": fps,
                "seg_failed": True,
                "reason": f"Too few frames: {total_frames} < {min_frames}",
                "mask_coverage_mean": 0.0
            }
        
        # Read all frames
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Upsample if needed
            if upsample > 1.0:
                h, w = frame.shape[:2]
                new_h, new_w = int(h * upsample), int(w * upsample)
                frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
            
            frames.append(frame)
        
        cap.release()
        
        if len(frames) < min_frames:
            return {
                "video_path": str(rel_path),
                "frames": len(frames),
                "fps": fps,
                "seg_failed": True,
                "reason": f"Too few frames after reading: {len(frames)} < {min_frames}",
                "mask_coverage_mean": 0.0
            }
        
        # YOLO face detection (sample every 5th frame until face found)
        face_box = None
        yolo_model = process_single_video.yolo_model
        if yolo_model is not None:
            for i in range(0, len(frames), 5):
                face_box = detect_face_yolo(yolo_model, frames[i])
                if face_box is not None:
                    break
        
        # Motion analysis
        motion_heatmap = np.zeros(frames[0].shape[:2], dtype=np.float32)
        for i in range(1, len(frames)):
            prev_gray = cv2.cvtColor(frames[i-1], cv2.COLOR_BGR2GRAY)
            curr_gray = cv2.cvtColor(frames[i], cv2.COLOR_BGR2GRAY)
            flow_mag = compute_optical_flow_magnitude(prev_gray, curr_gray)
            motion_heatmap += flow_mag
        
        motion_heatmap /= (len(frames) - 1)  # Average
        motion_box = get_motion_hotspot(motion_heatmap, motion_percentile, face_box)
        
        # SAM segmentation
        sam_predictor = process_single_video.sam_predictor
        masks = []
        
        # Process every stride-th frame
        for i in range(0, len(frames), stride):
            frame = frames[i]
            sam_predictor.set_image(frame)
            
            # Use face box if available, otherwise motion box
            prompt_box = face_box if face_box is not None else motion_box
            
            # Sample positive points in motion region
            positive_points = sample_points_in_box(motion_box, num_points=3)
            
            try:
                # Predict mask
                masks_pred, scores, _ = sam_predictor.predict(
                    point_coords=np.array(positive_points),
                    point_labels=np.ones(len(positive_points)),
                    box=np.array(prompt_box),
                    multimask_output=False
                )
                
                mask = masks_pred[0].astype(np.uint8) * 255
                
                # Keep largest connected component that intersects motion box
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if contours:
                    # Filter contours that intersect motion box
                    valid_contours = []
                    mx0, my0, mx1, my1 = motion_box
                    for contour in contours:
                        x, y, w, h = cv2.boundingRect(contour)
                        if not (x >= mx1 or x+w <= mx0 or y >= my1 or y+h <= my0):
                            valid_contours.append(contour)
                    
                    if valid_contours:
                        largest_contour = max(valid_contours, key=cv2.contourArea)
                        mask = np.zeros_like(mask)
                        cv2.fillPoly(mask, [largest_contour], 255)
                
                masks.append(mask)
                
            except Exception as e:
                # Fallback: create mask from motion box
                mask = np.zeros(frame.shape[:2], dtype=np.uint8)
                mx0, my0, mx1, my1 = motion_box
                mask[my0:my1, mx0:mx1] = 255
                masks.append(mask)
        
        # Interpolate and smooth masks
        masks = interpolate_masks(masks, stride)
        masks = smooth_masks(masks)
        
        # Ensure we have the right number of masks
        while len(masks) < len(frames):
            masks.append(masks[-1].copy())
        masks = masks[:len(frames)]
        
        # Calculate mask coverage
        frame_area = frames[0].shape[0] * frames[0].shape[1]
        coverages = [np.sum(mask > 0) / frame_area for mask in masks]
        mask_coverage_mean = np.mean(coverages)
        
        # Check for segmentation failure
        seg_failed = False
        reason = ""
        
        if mask_coverage_mean < min_mask_coverage:
            seg_failed = True
            reason = f"Coverage too low: {mask_coverage_mean:.4f} < {min_mask_coverage}"
        elif mask_coverage_mean > max_mask_coverage:
            seg_failed = True
            reason = f"Coverage too high: {mask_coverage_mean:.4f} > {max_mask_coverage}"
        elif np.sum([np.sum(mask > 0) == 0 for mask in masks]) > 0.25 * len(masks):
            seg_failed = True
            reason = "Missing masks on >25% of frames"
        
        # Save outputs
        # 1. Masked video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(masked_path), fourcc, fps, (size, size))
        
        for i, (frame, mask) in enumerate(zip(frames, masks)):
            # Apply mask
            masked_frame = frame.copy()
            masked_frame[mask == 0] = 0
            
            # Resize to target size
            masked_frame = cv2.resize(masked_frame, (size, size), interpolation=cv2.INTER_AREA)
            out.write(masked_frame)
        
        out.release()
        
        # 2. Save masks
        masks_array = np.stack(masks, axis=0)
        np.savez_compressed(str(masks_path), masks=masks_array)
        
        # 3. Save overlay images (3 evenly spaced frames)
        overlay_indices = [len(frames)//4, len(frames)//2, 3*len(frames)//4]
        for idx, frame_idx in enumerate(overlay_indices):
            frame = frames[frame_idx]
            mask = masks[frame_idx]
            
            # Create overlay
            overlay = frame.copy()
            overlay[mask > 0] = overlay[mask > 0] * 0.7 + np.array([0, 255, 0]) * 0.3
            
            overlay_path = overlays_dir / f"{video_stem}_overlay_{idx:02d}.png"
            cv2.imwrite(str(overlay_path), overlay)
        
        # 4. Save metrics
        metrics = {
            "frames": len(frames),
            "fps": fps,
            "face_box": face_box,
            "motion_box": motion_box,
            "mask_coverage_mean": mask_coverage_mean,
            "seg_failed": seg_failed,
            "reason": reason
        }
        
        with open(str(metrics_path), 'w') as f:
            json.dump(metrics, f, indent=2)
        
        # 5. Save fallback if failed
        if seg_failed:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(fallback_path), fourcc, fps, (size, size))
            
            for frame in frames:
                frame_resized = cv2.resize(frame, (size, size), interpolation=cv2.INTER_AREA)
                out.write(frame_resized)
            
            out.release()
        
        return {
            "video_path": str(rel_path),
            "frames": len(frames),
            "fps": fps,
            "mask_coverage_mean": mask_coverage_mean,
            "seg_failed": seg_failed,
            "reason": reason,
            "used_fallback": seg_failed
        }
        
    except Exception as e:
        return {"error": f"Processing failed for {video_path}: {e}"}


def generate_html_gallery(results: List[Dict], output_path: str, input_dir: str, output_dir: str):
    """Generate HTML gallery showing segmentation results."""

    # Sort results: failed first, then by coverage
    results_sorted = sorted(results, key=lambda x: (not x.get('seg_failed', False), x.get('mask_coverage_mean', 0)))

    template_str = """
<!DOCTYPE html>
<html>
<head>
    <title>Stage-3 Segmentation Gallery</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin: 10px 0; }
        .stat { background: #e3f2fd; padding: 10px; border-radius: 4px; }
        .video-card { background: white; margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50; }
        .video-card.failed { border-left-color: #f44336; }
        .video-info { margin-bottom: 10px; }
        .video-path { font-weight: bold; color: #1976d2; }
        .overlays { display: flex; gap: 10px; margin: 10px 0; }
        .overlay-img { max-width: 150px; border: 1px solid #ddd; border-radius: 4px; }
        .badges { display: flex; gap: 5px; margin: 5px 0; }
        .badge { padding: 2px 8px; border-radius: 12px; font-size: 12px; color: white; }
        .badge.pass { background: #4caf50; }
        .badge.fail { background: #f44336; }
        .badge.coverage { background: #2196f3; }
        .metrics { font-size: 12px; color: #666; margin: 5px 0; }
        .error { color: #f44336; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎭 Stage-3 Segmentation Results</h1>
        <div class="stats">
            <div class="stat">Total Videos: {{ total_videos }}</div>
            <div class="stat">Successful: {{ successful }} ({{ success_rate }}%)</div>
            <div class="stat">Failed: {{ failed }}</div>
            <div class="stat">Avg Coverage: {{ avg_coverage }}%</div>
        </div>
    </div>

    {% for result in results %}
    <div class="video-card {% if result.seg_failed %}failed{% endif %}">
        <div class="video-info">
            <div class="video-path">{{ result.video_path }}</div>
            <div class="badges">
                {% if result.seg_failed %}
                <span class="badge fail">FAILED</span>
                {% else %}
                <span class="badge pass">PASS</span>
                {% endif %}
                <span class="badge coverage">{{ "%.2f"|format(result.mask_coverage_mean * 100) }}% coverage</span>
            </div>
            {% if result.reason %}
            <div class="error">Reason: {{ result.reason }}</div>
            {% endif %}
        </div>

        <div class="metrics">
            Frames: {{ result.frames }} | FPS: {{ "%.1f"|format(result.fps) }}
            {% if result.used_fallback %} | Used Fallback{% endif %}
        </div>

        {% if not result.get('error') %}
        <div class="overlays">
            {% for i in range(3) %}
            {% set overlay_path = result.video_path | replace('.mp4', '') | replace('.mov', '') | replace('.mkv', '') | replace('.avi', '') | replace('.webm', '') %}
            {% set img_path = output_dir + '/' + overlay_path + '/overlays/' + overlay_path.split('/')[-1] + '_overlay_' + '%02d'|format(i) + '.png' %}
            <img src="{{ img_path }}" class="overlay-img" alt="Overlay {{ i }}">
            {% endfor %}
        </div>
        {% endif %}
    </div>
    {% endfor %}
</body>
</html>
    """

    # Calculate stats
    total_videos = len(results)
    successful = sum(1 for r in results if not r.get('seg_failed', True))
    failed = total_videos - successful
    success_rate = (successful / total_videos * 100) if total_videos > 0 else 0
    avg_coverage = np.mean([r.get('mask_coverage_mean', 0) for r in results]) * 100

    template = Template(template_str)
    html_content = template.render(
        results=results_sorted,
        total_videos=total_videos,
        successful=successful,
        failed=failed,
        success_rate=f"{success_rate:.1f}",
        avg_coverage=f"{avg_coverage:.2f}",
        output_dir=output_dir
    )

    with open(output_path, 'w') as f:
        f.write(html_content)


def save_summary_csv(results: List[Dict], output_path: str):
    """Save summary CSV with processing results."""
    with open(output_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['video_path', 'frames', 'fps', 'mask_coverage_mean', 'seg_failed', 'reason', 'used_fallback'])

        for result in results:
            if 'error' not in result:
                writer.writerow([
                    result.get('video_path', ''),
                    result.get('frames', 0),
                    result.get('fps', 0.0),
                    result.get('mask_coverage_mean', 0.0),
                    result.get('seg_failed', True),
                    result.get('reason', ''),
                    result.get('used_fallback', False)
                ])


def save_manifest_csv(results: List[Dict], output_path: str, input_dir: str, output_dir: str):
    """Save training manifest CSV."""
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    with open(output_path, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['path_masked', 'path_masks', 'phrase', 'demog_cell', 'width', 'height',
                        'frames', 'fps', 'mask_coverage_mean', 'seg_failed'])

        for result in results:
            if 'error' not in result:
                video_path = result.get('video_path', '')
                if video_path:
                    # Extract phrase from path
                    phrase = extract_phrase_from_path(Path(video_path))

                    # Build output paths
                    video_stem = Path(video_path).stem
                    rel_dir = Path(video_path).parent

                    path_masked = str(Path(output_dir) / rel_dir / f"{video_stem}_masked.mp4")
                    path_masks = str(Path(output_dir) / rel_dir / f"{video_stem}_masks.npz")

                    writer.writerow([
                        path_masked,
                        path_masks,
                        phrase,
                        '',  # demog_cell - to be filled later
                        96,  # width
                        96,  # height
                        result.get('frames', 0),
                        result.get('fps', 0.0),
                        result.get('mask_coverage_mean', 0.0),
                        result.get('seg_failed', True)
                    ])


def main():
    parser = argparse.ArgumentParser(description='Stage-3 Segmentation: YOLO + SAM')
    parser.add_argument('--input_dir', required=True, help='Input directory with motion-refined crops')
    parser.add_argument('--output_dir', required=True, help='Output directory for segmented results')
    parser.add_argument('--gallery', required=True, help='HTML gallery output path')
    parser.add_argument('--summary', required=True, help='CSV summary output path')
    parser.add_argument('--manifest', required=True, help='Training manifest CSV output path')
    parser.add_argument('--yolo_model', default='yolov8n-face.pt', help='YOLO model path')
    parser.add_argument('--sam_model', default='vit_b', choices=['vit_b', 'vit_l', 'vit_h'], help='SAM model type')
    parser.add_argument('--size', type=int, default=96, help='Output video size')
    parser.add_argument('--upsample', type=float, default=2.0, help='Upsample factor for analysis')
    parser.add_argument('--motion_percentile', type=int, default=85, help='Motion threshold percentile')
    parser.add_argument('--min_mask_coverage', type=float, default=0.015, help='Minimum mask coverage')
    parser.add_argument('--max_mask_coverage', type=float, default=0.12, help='Maximum mask coverage')
    parser.add_argument('--min_frames', type=int, default=12, help='Minimum frames required')
    parser.add_argument('--stride', type=int, default=2, help='Frame stride for SAM processing')
    parser.add_argument('--workers', type=int, default=4, help='Number of worker processes')

    args = parser.parse_args()

    print("🎭 Stage-3 Segmentation: YOLO + SAM")
    print(f"Input: {args.input_dir}")
    print(f"Output: {args.output_dir}")
    print(f"Workers: {args.workers}")

    # Create output directories
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(os.path.dirname(args.gallery), exist_ok=True)
    os.makedirs(os.path.dirname(args.summary), exist_ok=True)

    # Find videos
    extensions = ['.mp4', '.mov', '.mkv', '.avi', '.webm']
    videos = find_videos(args.input_dir, extensions)
    print(f"📹 Found {len(videos)} videos")

    if len(videos) == 0:
        print("❌ No videos found!")
        return

    # Prepare arguments for multiprocessing
    process_args = [
        (video, args.input_dir, args.output_dir, args.yolo_model, args.sam_model,
         args.size, args.upsample, args.motion_percentile, args.min_mask_coverage,
         args.max_mask_coverage, args.min_frames, args.stride)
        for video in videos
    ]

    # Process videos
    print("🔄 Processing videos...")
    if args.workers > 1:
        with mp.Pool(args.workers) as pool:
            results = list(tqdm(pool.imap(process_single_video, process_args), total=len(videos)))
    else:
        results = [process_single_video(args) for args in tqdm(process_args)]

    # Filter out errors and log them
    errors = [r for r in results if 'error' in r]
    if errors:
        error_log_path = os.path.join(os.path.dirname(args.summary), 'stage3_skips.txt')
        with open(error_log_path, 'w') as f:
            for error in errors:
                f.write(f"{error['error']}\n")
        print(f"⚠️  {len(errors)} errors logged to {error_log_path}")

    valid_results = [r for r in results if 'error' not in r]

    # Generate reports
    print("📊 Generating reports...")
    generate_html_gallery(valid_results, args.gallery, args.input_dir, args.output_dir)
    save_summary_csv(valid_results, args.summary)
    save_manifest_csv(valid_results, args.manifest, args.input_dir, args.output_dir)

    # Print summary
    total = len(valid_results)
    successful = sum(1 for r in valid_results if not r.get('seg_failed', True))
    success_rate = (successful / total * 100) if total > 0 else 0
    avg_coverage = np.mean([r.get('mask_coverage_mean', 0) for r in valid_results]) * 100

    print(f"\n✅ Processing complete!")
    print(f"📈 Success rate: {successful}/{total} ({success_rate:.1f}%)")
    print(f"📊 Average mask coverage: {avg_coverage:.2f}%")
    print(f"🎨 Gallery: {args.gallery}")
    print(f"📋 Summary: {args.summary}")
    print(f"📝 Manifest: {args.manifest}")


if __name__ == '__main__':
    main()
