
<!DOCTYPE html>
<html>
<head>
    <title>Stage-3 Segmentation Gallery</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin: 10px 0; }
        .stat { background: #e3f2fd; padding: 10px; border-radius: 4px; }
        .video-card { background: white; margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50; }
        .video-card.failed { border-left-color: #f44336; }
        .video-info { margin-bottom: 10px; }
        .video-path { font-weight: bold; color: #1976d2; }
        .overlays { display: flex; gap: 10px; margin: 10px 0; }
        .overlay-img { max-width: 150px; border: 1px solid #ddd; border-radius: 4px; }
        .badges { display: flex; gap: 5px; margin: 5px 0; }
        .badge { padding: 2px 8px; border-radius: 12px; font-size: 12px; color: white; }
        .badge.pass { background: #4caf50; }
        .badge.fail { background: #f44336; }
        .badge.coverage { background: #2196f3; }
        .metrics { font-size: 12px; color: #666; margin: 5px 0; }
        .error { color: #f44336; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎭 Stage-3 Segmentation Results</h1>
        <div class="stats">
            <div class="stat">Total Videos: 0</div>
            <div class="stat">Successful: 0 (0.0%)</div>
            <div class="stat">Failed: 0</div>
            <div class="stat">Avg Coverage: nan%</div>
        </div>
    </div>

    
</body>
</html>
    